"""
Views for the student services appointment system.

This module contains views for:
- Authentication (login, logout, registration)
- Role-based dashboards
- User profile management
"""

from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.urls import reverse
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.core.exceptions import ValidationError
from .decorators import student_required, staff_required, admin_required
from .models import UserProfile, Department


def home_view(request):
    """
    Home page view that redirects authenticated users to their dashboard.
    """
    if request.user.is_authenticated:
        if hasattr(request.user, 'userprofile') and request.user.userprofile.role:
            return redirect(request.user.userprofile.get_dashboard_url())
        else:
            messages.warning(request, "Your profile is incomplete. Please contact an administrator.")

    return render(request, 'appointments/home.html')


@csrf_protect
@require_http_methods(["GET", "POST"])
def login_view(request):
    """
    Custom login view with role-based dashboard redirection.
    """
    if request.user.is_authenticated:
        # User is already logged in, redirect to their dashboard
        if hasattr(request.user, 'userprofile') and request.user.userprofile.role:
            return redirect(request.user.userprofile.get_dashboard_url())
        else:
            messages.warning(request, "Your profile is incomplete. Please contact an administrator.")
            return render(request, 'appointments/auth/login.html')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        if not username or not password:
            messages.error(request, "Please provide both username and password.")
            return render(request, 'appointments/auth/login.html')

        # Authenticate user
        user = authenticate(request, username=username, password=password)

        if user is not None:
            if user.is_active:
                login(request, user)

                # Check if user has a complete profile
                if hasattr(user, 'userprofile') and user.userprofile.role:
                    messages.success(request, f"Welcome back, {user.userprofile.display_name}!")

                    # Redirect to next URL if provided, otherwise to dashboard
                    next_url = request.GET.get('next')
                    if next_url:
                        return redirect(next_url)
                    else:
                        return redirect(user.userprofile.get_dashboard_url())
                else:
                    messages.warning(request, "Your profile is incomplete. Please contact an administrator.")
                    return render(request, 'appointments/auth/login.html')
            else:
                messages.error(request, "Your account has been deactivated. Please contact an administrator.")
        else:
            messages.error(request, "Invalid username or password.")

    return render(request, 'appointments/auth/login.html')


@login_required
def logout_view(request):
    """
    Logout view that clears the session and redirects to home.
    """
    user_name = request.user.userprofile.display_name if hasattr(request.user, 'userprofile') else request.user.username
    logout(request)
    messages.success(request, f"Goodbye, {user_name}! You have been logged out successfully.")
    return redirect('home')


@csrf_protect
@require_http_methods(["GET", "POST"])
def register_view(request):
    """
    User registration view with role assignment.
    Note: In a production system, role assignment would typically be handled by administrators.
    """
    if request.user.is_authenticated:
        return redirect('home')

    if request.method == 'POST':
        # Get form data
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        password_confirm = request.POST.get('password_confirm')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        role = request.POST.get('role')
        student_id = request.POST.get('student_id')
        department_id = request.POST.get('department_id')
        phone_number = request.POST.get('phone_number')

        # Validate required fields
        if not all([username, email, password, password_confirm, first_name, last_name, role]):
            messages.error(request, "Please fill in all required fields.")
            return render(request, 'appointments/auth/register.html', {
                'departments': Department.objects.filter(is_active=True).order_by('name')
            })

        # Validate password confirmation
        if password != password_confirm:
            messages.error(request, "Passwords do not match.")
            return render(request, 'appointments/auth/register.html', {
                'departments': Department.objects.filter(is_active=True).order_by('name')
            })

        # Check if username already exists
        if User.objects.filter(username=username).exists():
            messages.error(request, "Username already exists. Please choose a different one.")
            return render(request, 'appointments/auth/register.html', {
                'departments': Department.objects.filter(is_active=True).order_by('name')
            })

        # Check if email already exists
        if User.objects.filter(email=email).exists():
            messages.error(request, "Email already exists. Please use a different email address.")
            return render(request, 'appointments/auth/register.html', {
                'departments': Department.objects.filter(is_active=True).order_by('name')
            })

        try:
            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )

            # Update user profile with role and additional info
            profile = user.userprofile
            profile.role = role
            profile.phone_number = phone_number

            # Role-specific validations and assignments
            if role == 'student':
                if not student_id:
                    messages.error(request, "Student ID is required for student accounts.")
                    user.delete()  # Clean up created user
                    return render(request, 'appointments/auth/register.html', {
                        'departments': Department.objects.filter(is_active=True).order_by('name')
                    })
                profile.student_id = student_id

            elif role == 'office_staff':
                if not department_id:
                    messages.error(request, "Department assignment is required for office staff accounts.")
                    user.delete()  # Clean up created user
                    return render(request, 'appointments/auth/register.html', {
                        'departments': Department.objects.filter(is_active=True).order_by('name')
                    })
                try:
                    department = Department.objects.get(id=department_id, is_active=True)
                    profile.department = department
                except Department.DoesNotExist:
                    messages.error(request, "Invalid department selection.")
                    user.delete()  # Clean up created user
                    return render(request, 'appointments/auth/register.html', {
                        'departments': Department.objects.filter(is_active=True).order_by('name')
                    })

            # Save the profile
            profile.save()

            messages.success(request, "Account created successfully! You can now log in.")
            return redirect('login')

        except ValidationError as e:
            messages.error(request, f"Registration failed: {e.message}")
            user.delete() if 'user' in locals() else None
        except Exception as e:
            messages.error(request, f"An error occurred during registration: {str(e)}")
            user.delete() if 'user' in locals() else None

    return render(request, 'appointments/auth/register.html', {
        'departments': Department.objects.filter(is_active=True).order_by('name')
    })


# Dashboard Views

@student_required
def student_dashboard(request):
    """
    Student dashboard view showing student-specific information and actions.
    """
    profile = request.user.userprofile

    context = {
        'user_profile': profile,
        'page_title': 'Student Dashboard',
        'dashboard_type': 'student'
    }

    return render(request, 'appointments/dashboards/student.html', context)


@staff_required
def staff_dashboard(request):
    """
    Office staff dashboard view showing staff-specific information and actions.
    """
    profile = request.user.userprofile

    context = {
        'user_profile': profile,
        'department': profile.department,
        'page_title': 'Staff Dashboard',
        'dashboard_type': 'staff'
    }

    return render(request, 'appointments/dashboards/staff.html', context)


@admin_required
def admin_dashboard(request):
    """
    Administrator dashboard view showing admin-specific information and actions.
    """
    profile = request.user.userprofile

    # Get comprehensive statistics for the admin dashboard
    total_users = User.objects.count()
    total_students = UserProfile.objects.filter(role='student').count()
    total_staff = UserProfile.objects.filter(role='office_staff').count()
    total_departments = Department.objects.filter(is_active=True).count()

    # Get appointment statistics (if Appointment model exists)
    total_appointments = 0
    try:
        from .models import Appointment
        total_appointments = Appointment.objects.count()
    except (ImportError, AttributeError):
        # Appointment model doesn't exist yet
        pass

    # System alerts (placeholder for now)
    system_alerts = 0  # This can be enhanced later with actual alert logic

    context = {
        'user_profile': profile,
        'page_title': 'Administrator Dashboard',
        'dashboard_type': 'admin',
        'stats': {
            'total_users': total_users,
            'total_students': total_students,
            'total_staff': total_staff,
            'total_departments': total_departments,
            'total_appointments': total_appointments,
            'system_alerts': system_alerts,
        }
    }

    return render(request, 'appointments/dashboards/admin.html', context)


@login_required
def profile_view(request):
    """
    User profile view for viewing and editing profile information.
    """
    profile = request.user.userprofile

    context = {
        'user_profile': profile,
        'page_title': 'My Profile',
        'departments': Department.objects.filter(is_active=True).order_by('name')
    }

    return render(request, 'appointments/profile.html', context)
